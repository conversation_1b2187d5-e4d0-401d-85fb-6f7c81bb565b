# 這個github action我想要寫一個排程，每年執行一次左右，用來抓取某個網站的資料，並且存到資料庫中
name: Run Java

on:
  workflow_dispatch: # 可以手動執行此 action

permissions: write-all

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: "microsoft"
          java-version: "21"
          cache: "maven"

      # chatgpt建議，如果有mvnw檔案，可以直接使用mvnw
      # unix環境下要先給mvnw檔案執行權限
      # github ubuntu-latest環境下，預設是有安裝maven的也可以直接使用mvn
      # 使用 mvnw，可確保使用專案內指定的 Maven 版本
      - name: Grant execute permission to mvnw
        run: chmod +x mvnw

      - name: Build with Maven
        run: ./mvnw clean compile dependency:copy-dependencies

      - name: Run Java
        run: java -cp "target/classes:target/dependency/*" com.vance.demo.tool.MarkdownToHtml

      # - name: Convert Markdown to PDF
      # - uses: baileyjm02/markdown-to-pdf@v1
      #   with:
      #     input_dir: docs
      #     output_dir: pdfs
      #     images_dir: docs/images
      #     # for example <img src="./images/file-name.png">
      #     image_import: ./images
      #     # Default is true, can set to false to only get PDF files
      #     build_html: false
      # - uses: actions/upload-artifact@v4
      #   with:
      #     name: docs
      #     path: pdfs
