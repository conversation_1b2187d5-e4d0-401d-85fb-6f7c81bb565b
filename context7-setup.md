# Context 7 MCP 安裝完成

## 🎉 安裝狀態
✅ Context 7 MCP 已成功安裝並設定完成！

## 📋 已完成的設定

### 1. VS Code 設定
- 已更新 `.vscode/settings.json` 檔案
- 新增 MCP 伺服器設定，使用 stdio 傳輸模式

### 2. Cursor 設定
- 已建立 `.cursor/mcp.json` 檔案
- 設定 Context 7 MCP 伺服器

### 3. 系統需求確認
- ✅ Node.js v22.17.1 (需求: >= v18.0.0)
- ✅ npm v10.9.2
- ✅ Context 7 MCP 套件可正常執行

## 🚀 如何使用 Context 7

### 基本使用方式
在您的 AI 程式碼助手提示中加入 `use context7`：

```
建立一個 Next.js 中介軟體來檢查 cookies 中的有效 JWT，並將未驗證的使用者重新導向到 `/login`。use context7
```

### 使用特定函式庫 ID
如果您知道確切的函式庫，可以直接使用其 Context 7 ID：

```
使用 supabase 實作基本驗證。use library /supabase/supabase for api and docs
```

## 🛠️ 可用工具

Context 7 MCP 提供以下工具：

1. **resolve-library-id**: 將一般函式庫名稱解析為 Context 7 相容的函式庫 ID
   - `libraryName` (必需): 要搜尋的函式庫名稱

2. **get-library-docs**: 使用 Context 7 相容的函式庫 ID 獲取文件
   - `context7CompatibleLibraryID` (必需): 確切的 Context 7 相容函式庫 ID
   - `topic` (選用): 專注於特定主題的文件
   - `tokens` (選用, 預設 10000): 回傳的最大 token 數量

## 💡 使用技巧

### 自動規則設定
如果您不想每次都加入 `use context7`，可以在編輯器中設定規則：

**Cursor 規則** (在 Cursor Settings > Rules 中)：
```
[[calls]]
match = "when the user requests code examples, setup or configuration steps, or library/API documentation"
tool = "context7"
```

**Windsurf 規則** (在 `.windsurfrules` 檔案中)：
```
[[calls]]
match = "when the user requests code examples, setup or configuration steps, or library/API documentation"
tool = "context7"
```

## 🔧 測試安裝

要測試 Context 7 是否正常工作，可以執行：

```powershell
npx @modelcontextprotocol/inspector npx @upstash/context7-mcp
```

這會啟動 MCP Inspector 並在瀏覽器中開啟測試介面。

## 📚 更多資訊

- 官方網站: https://context7.com
- GitHub 倉庫: https://github.com/upstash/context7
- 支援的編輯器: VS Code, Cursor, Windsurf, Claude Desktop, Zed 等

## ⚠️ 注意事項

- Context 7 專案由社群貢獻，請謹慎使用
- 如發現可疑或有害內容，請使用專案頁面的「回報」按鈕
- 使用 Context 7 時請自行承擔風險

---

🎊 **恭喜！您現在可以開始使用 Context 7 來獲取最新的程式碼文件和範例了！**
