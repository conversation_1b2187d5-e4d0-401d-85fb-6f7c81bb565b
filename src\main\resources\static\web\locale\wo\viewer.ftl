# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.


## Main toolbar buttons (tooltips and alt text for images)

pdfjs-previous-button =
    .title = Xët wi jiitu
pdfjs-previous-button-label = Bi jiitu
pdfjs-next-button =
    .title = Xët wi ci topp
pdfjs-next-button-label = Bi ci topp
pdfjs-zoom-out-button =
    .title = Wàññi
pdfjs-zoom-out-button-label = Wàññi
pdfjs-zoom-in-button =
    .title = Yaatal
pdfjs-zoom-in-button-label = Yaatal
pdfjs-zoom-select =
    .title = Yambalaŋ
pdfjs-presentation-mode-button =
    .title = Wañarñil ci anamu wone
pdfjs-presentation-mode-button-label = <PERSON><PERSON> Wone
pdfjs-open-file-button =
    .title = <PERSON><PERSON><PERSON> benn den<PERSON>
pdfjs-open-file-button-label = <PERSON>bbi
pdfjs-print-button =
    .title = Móol
pdfjs-print-button-label = Móol

##  Secondary toolbar and context menu


## Document properties dialog

pdfjs-document-properties-title = Bopp:

## Variables:
##   $width (Number) - the width of the (current) page
##   $height (Number) - the height of the (current) page
##   $unit (String) - the unit of measurement of the (current) page
##   $name (String) - the name of the (current) page
##   $orientation (String) - the orientation of the (current) page


##


## Print

pdfjs-printing-not-supported = Artu: Joowkat bii nanguwul lool mool.

## Tooltips and alt text for side panel toolbar buttons

pdfjs-thumbs-button =
    .title = Wone nataal yu ndaw yi
pdfjs-thumbs-button-label = Nataal yu ndaw yi
pdfjs-findbar-button =
    .title = Gis ci biir jukki bi
pdfjs-findbar-button-label = Wut

## Thumbnails panel item (tooltip and alt text for images)

# Variables:
#   $page (Number) - the page number
pdfjs-thumb-page-title =
    .title = Xët { $page }
# Variables:
#   $page (Number) - the page number
pdfjs-thumb-page-canvas =
    .aria-label = Wiñet bu xët { $page }

## Find panel button title and messages

pdfjs-find-previous-button =
    .title = Seet beneen kaddu bu ni mel te jiitu
pdfjs-find-previous-button-label = Bi jiitu
pdfjs-find-next-button =
    .title = Seet beneen kaddu bu ni mel
pdfjs-find-next-button-label = Bi ci topp
pdfjs-find-highlight-checkbox = Melaxal lépp
pdfjs-find-match-case-checkbox-label = Sàmm jëmmalin wi
pdfjs-find-reached-top = Jot nañu ndorteel xët wi, kontine dale ko ci suuf
pdfjs-find-reached-bottom = Jot nañu jeexitalu xët wi, kontine ci ndorte
pdfjs-find-not-found = Gisiñu kaddu gi

## Predefined zoom values

pdfjs-page-scale-width = Yaatuwaay bu mët
pdfjs-page-scale-fit = Xët lëmm
pdfjs-page-scale-auto = Yambalaŋ ci saa si
pdfjs-page-scale-actual = Dayo bi am

## PDF page


## Loading indicator messages

pdfjs-loading-error = Am na njumte ci yebum dencukaay PDF bi.
pdfjs-invalid-file-error = Dencukaay PDF bi baaxul walla mu sankar.
pdfjs-rendering-error = Am njumte bu am bi xët bi di wonewu.

## Annotations

# .alt: This is used as a tooltip.
# Variables:
#   $type (String) - an annotation type from a list defined in the PDF spec
# (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
pdfjs-text-annotation-type =
    .alt = [Karmat { $type }]

## Password

pdfjs-password-ok-button = OK
pdfjs-password-cancel-button = Neenal

## Editing


## Default editor aria labels


## Remove button for the various kind of editor.


##


## Alt-text dialog


## Editor resizers
## This is used in an aria label to help to understand the role of the resizer.


## Color picker


## Show all highlights
## This is a toggle button to show/hide all the highlights.


## New alt-text dialog
## Group note for entire feature: Alternative text (alt text) helps when people can't see the image. This feature includes a tool to create alt text automatically using an AI model that works locally on the user's device to preserve privacy.


## Image alt-text settings


## "Annotations removed" bar


## Add a signature dialog


## Tab names


## Tab panels


## Controls


## Dialog buttons


## Main menu for adding/removing signatures


## Editor toolbar


## Edit signature description dialog

