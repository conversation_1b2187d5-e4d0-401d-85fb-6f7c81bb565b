name: Notify All Collaborators on PR
on:
  pull_request:
    types: [closed] # 當 PR 被關閉（合併或關閉）時觸發
  # workflow_dispatch: # 可以手動執行此 action

jobs:
  notify:
    runs-on: ubuntu-latest
    if: ${{ false }} # 永遠不執行
    permissions:
      pull-requests: write # 授予寫入 PR 評論的權限
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get Collaborators
        id: get-collaborators
        run: |
          # 使用 GitHub API 獲取 collaborators 列表
          curl -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
               -H "Accept: application/vnd.github+json" \
               https://api.github.com/repos/${{ github.repository }}/collaborators > collaborators.json
          # 提取使用者名稱並格式化為 @mention
          COLLABORATORS=$(jq -r '.[] | "@" + .login' collaborators.json | tr '\n' ' ')
          echo "collaborators=$COLLABORATORS" >> $GITHUB_OUTPUT

      - name: Comment on PR
        uses: actions/github-script@v6
        with:
          script: |
            const collaborators = "${{ steps.get-collaborators.outputs.collaborators }}";
            const message = `PR #${{ github.event.pull_request.number }} has been completed! Notifying: ${collaborators}`;
            github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: message
            });
