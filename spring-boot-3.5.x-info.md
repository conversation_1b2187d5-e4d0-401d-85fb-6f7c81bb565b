# Spring Boot 3.5.x 資訊報告

## 📋 查詢結果概要

透過 Context 7 MCP 查詢到的 Spring Boot 3.5.x 相關資訊：

### 🔍 可用的 Spring Boot 資源

#### 1. 主要 Spring Boot 專案
- **Context7 ID**: `/spring-projects/spring-boot`
- **描述**: Spring Boot 官方專案，幫助建立 Spring 驅動的生產級應用程式
- **程式碼範例**: 956 個
- **可用版本**: v2.5.5, v3.4.1, v2.7.18, v3.3.11, **v3.5.3**, v3.1.12

#### 2. Spring Boot 3.5.3 API 文件
- **Context7 ID**: `/context7/spring_io-spring-boot-api-java`
- **描述**: 提供 Spring Boot 3.5.3 的 Java API 文件
- **程式碼範例**: 18,236 個
- **信任分數**: 10/10

#### 3. Spring Boot 3.X Kafka Streams
- **Context7 ID**: `/packtpublishing/kafka-streams-api-for-developers-using-java-spring-boot-3.x`
- **描述**: 針對 Java/Spring Boot 3.X 開發者的 Kafka Streams API
- **程式碼範例**: 85 個
- **信任分數**: 9.1/10

## 🚀 Spring Boot 3.5.x 重要特性

### 系統需求
- **Java 版本**: 需要 Java SDK v17 或更高版本
- **檢查指令**: `java -version`

### 核心功能

#### 1. 屬性遷移工具
Spring Boot 3.5.x 提供屬性遷移工具來協助升級：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-properties-migrator</artifactId>
    <scope>runtime</scope>
</dependency>
```

#### 2. Actuator 增強功能
- **HTTP Exchange Recording**: 新的配置屬性用於自訂 HTTP 交換記錄
- **配置屬性**:
  - `management.httpexchanges.recording.include`: 指定要包含的項目
  - `management.httpexchanges.recording.enabled`: 啟用/停用記錄功能

#### 3. 建置資訊生成
支援透過 Maven 或 Gradle 外掛程式生成建置資訊：

**Maven 配置**:
```xml
<build>
    <plugins>
        <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <version>{version-spring-boot}</version>
            <executions>
                <execution>
                    <goals>
                        <goal>build-info</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>
    </plugins>
</build>
```

**Gradle 配置**:
```gradle
springBoot {
    buildInfo()
}
```

#### 4. Banner 變數支援
Spring Boot 3.5.x 支援以下 Banner 變數：
- `${application.version}`: 應用程式版本
- `${application.formatted-version}`: 格式化的應用程式版本
- `${spring-boot.version}`: Spring Boot 版本
- `${spring-boot.formatted-version}`: 格式化的 Spring Boot 版本
- `${Ansi.NAME}`: ANSI 轉義碼支援

#### 5. Flyway 資料庫遷移
增強的 Flyway 配置選項：
- `spring.flyway.url`: 指定 JDBC URL
- `spring.flyway.user`: 資料庫使用者名稱
- `spring.flyway.password`: 資料庫密碼
- `spring.flyway.locations`: 遷移腳本位置

#### 6. PropertiesLauncher 配置
支援多種啟動器配置屬性：
- `loader.path`: 類路徑配置
- `loader.home`: 解析相對路徑的基礎目錄
- `loader.args`: 主方法的預設參數
- `loader.main`: 要啟動的主類別名稱

## 📦 依賴管理

### Actuator 依賴
```xml
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
</dependencies>
```

## 🔧 開發工具

### SDKMAN! 版本管理
```shell
$ sdk ls springboot
```

### CLI 版本檢查
```bash
java -version
```

## 📚 學習資源

1. **官方文件**: Spring Boot 3.5.3 API 文件提供完整的類別和方法說明
2. **程式碼範例**: 超過 18,000 個程式碼範例可供參考
3. **社群資源**: Kafka Streams 整合指南和最佳實踐

## 🎯 升級建議

1. **版本檢查**: 確保使用 Java 17 或更高版本
2. **屬性遷移**: 使用 `spring-boot-properties-migrator` 協助升級
3. **測試**: 充分測試 Actuator 和其他新功能
4. **文件參考**: 利用 Context 7 獲取最新的 API 文件

---

*此報告基於 Context 7 MCP 於 2025-07-28 查詢的最新資訊*
