name: Test Telegram Bot

on:
  workflow_dispatch: # 可以手動執行此 action
    inputs:
      message:
        description: "欲發送的訊息" # 輸入欄位的描述
        required: true # 是否為必要欄位
        default: "這是從 GitHub Actions 發送的測試訊息！" # 預設值（可選）

jobs:
  test-bot:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Run Telegram bot test
        env:
          TELEGRAM_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
          TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
        run: |
          MESSAGE="${{ github.event.inputs.message }}"
          curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_TOKEN}/sendMessage" \
            -d "chat_id=${TELEGRAM_CHAT_ID}" \
            -d "text=${MESSAGE}"
