name: Create Release

on:
  push:
    tags:
      - "v*.*.*"

permissions:
  contents: write
  pull-requests: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: 依條件設定分支名稱
        id: branch
        env:
          ALLOWED_BRANCHES: "master"
        run: |
          # 取得所有分支
          git fetch --all 
          # 依條件取得分支名稱
          BRANCH_NAME=$(git branch -r --contains ${{ github.sha }} | grep -Eo '(${{env.ALLOWED_BRANCHES}})' | head -n 1)
          echo "Tag Commit: ${{ github.sha }}"
          echo "允分支名稱: ${{env.ALLOWED_BRANCHES}}"
          echo "======================================="
          git branch -r --contains ${{ github.sha }}
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_OUTPUT

      - name: Release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/') && steps.branch.outputs.BRANCH_NAME != ''
        with:
          generate_release_notes: true # true 產生 release notes
          prerelease: false # false 正式發佈版本 (Stable Release
          draft: false # false 直接發佈
