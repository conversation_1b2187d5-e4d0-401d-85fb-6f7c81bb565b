name: Translate (googletrans)

on:
  # push:
  #   branches:
  #     - develop
  #   paths:
  #     - "README.md"
  workflow_dispatch: # 可以手動執行此 action
    inputs:
      lang_list:
        description: "Comma-separated list of target languages (e.g., zh-cn, en)"
        required: true
        default: "zh-cn, en"

env:
  DEFAULT_LANG_LIST: "zh-cn, en"

jobs:
  translate:
    runs-on: ubuntu-latest
    steps:
      - name: Check branch
        if: github.ref != 'refs/heads/develop'
        run: |
          echo "This workflow is only allowed on the develop branch."
          exit 1

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12" # Specify the Python version you want to use

      - name: Install googletrans
        run: pip install googletrans==4.0.0-rc1

      - name: Determine language list
        id: lang_list
        run: echo "LANG_LIST=${{ github.event.inputs.lang_list || env.DEFAULT_LANG_LIST }}" >> $GITHUB_OUTPUT

      - name: Translate document
        env:
          LANG_LIST: ${{ steps.lang_list.outputs.LANG_LIST }}
        run: |
          python -c "
          from googletrans import Translator
          import os
          translator = Translator(service_urls=['translate.google.com'])
          with open('README.md', 'r', encoding='utf-8') as f:
              text = f.read()
          lang_list = os.getenv('LANG_LIST').replace(' ', '').split(',')
          for lang in lang_list:
              translated = translator.translate(text, dest=lang)
              output_file = f'README_{lang}.md'
              with open(output_file, 'w', encoding='utf-8') as f:
                  f.write(translated.text)
              print(f'Translated to {lang} and saved as {output_file}')
          "
      - name: Commit translated files
        run: |
          git config user.name "GitHub Action"
          git config user.email "<EMAIL>"
          git add README_*.md
          git commit -m "Translate README.md to multiple languages: ${{ steps.lang_list.outputs.LANG_LIST }}"
          git push
        continue-on-error: true
