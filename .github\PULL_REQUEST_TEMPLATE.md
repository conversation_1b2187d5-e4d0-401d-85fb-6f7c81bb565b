## 📝 描述 (Description)

**將這支 PR 所做的事情，取代這段文字，描述越詳細越好。**

## 🔗 相關問題 (Related Issue)

close #<issue_number> 或 N/A

## 🚨 這是否會引入不相容的變更? (Breaking Change)

當開發人員從 main 分支合併並執行時，是否會產生錯誤？如果不確定，請嘗試在舊環境上測試，以確保系統穩定性。

- [ ] ⚠️ **是**，這將導致現有功能發生變更或錯誤。
- [ ] ✅ **否**，這不會影響現有功能。

## 🔄 變更類型 (Type of change)

- [ ] 🐛 **fix**      錯誤修復（Bug 修正）
- [ ] ✨ **feat**     新功能（新增功能或改進）
- [ ] 🎨 **style**    代碼風格更新（格式化、本地變數、排版）
- [ ] 🔧 **refactor** 重構（不是新功能或修復錯誤）
- [ ] 📚 **docs**     文件內容變更（說明文件或註解）
- [ ] 🚀 **perf**     效能優化（提升執行速度或降低資源消耗）
- [ ] 🧪 **test**     測試相關（新增或更新測試）
- [ ] ⚙️ **ci/cd**    CI/CD 相關變更（例如 GitHub Actions、Jenkins）
- [ ] 🏗️ **build**    建置相關變更（例如修改系統參數或 Maven 配置）
- [ ] 🧹 **chore**    其他，請具體說明：

## ✅ 檢查清單 (Checklist)

在提交 Pull Request 之前，請確認以下事項：

- [ ] 🔍 **我已經充分測試了我的變更**：我確認已運行代碼並手動測試所有受影響的區域。
- [ ] ✔️ **所有現有測試都通過**：我已運行所有測試並確認沒有任何功能被破壞。
- [ ] 🎯 **我遵循了編碼規範**：我的代碼符合儲存庫中定義的風格指南和編碼規範。
- [ ] 📚 **我已記錄我的變更**：我已經更新了文件，或者我的更改不需要更新文件。
- [ ] 📋 **PR 標題與內容描述"**：我的 PR 有明確的標題與內容描述。

## 🛠 附加資訊 (Additional context)

N/A

<!--
如果有任何額外資訊可以幫助審查此 PR，請在這裡補充，例如：
- 這項變更的設計考量或技術決策
- 相關截圖或日誌
- 影響範圍或相依性分析
如果沒有額外資訊，可以留空此區塊。
-->
